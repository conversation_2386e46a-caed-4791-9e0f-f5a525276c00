<template>
	<div>
		<el-row>
			<div class="mt8 w-full">
				<div class="float-left">
					<el-form :model="state.queryForm" ref="queryRef" :inline="true" @keyup.enter="getDataList">
						<el-form-item label="所属仓库" prop="warehouse">
							<el-select v-model="state.queryForm.warehouseId" placeholder="请选择所属仓库" clearable class="w-80">
								<el-option v-for="item in warehouseData" :key="item.index" :label="item.warehouseName" :value="item.id" />
							</el-select>
						</el-form-item>
						<el-form-item label="mac" prop="mac">
							<el-input placeholder="请输入mac" v-model="state.queryForm.mac" style="max-width: 180px" clearable />
						</el-form-item>

						<el-form-item label="位置编码" prop="locationCode">
							<el-input placeholder="请输入位置编码" v-model="state.queryForm.locationCode" style="max-width: 180px" clearable />
						</el-form-item>
						<el-form-item label="电池低于" prop="power">
							<el-input placeholder="请输入电池低于" v-model="state.queryForm.power" style="max-width: 180px" clearable />
						</el-form-item>
						<el-form-item>
							<el-button icon="search" type="primary" @click="getDataList"> 查询 </el-button>
						</el-form-item>
					</el-form>
				</div>
				<div class="float-right">
					<el-button type="primary" class="ml10" @click="openClick"> 一键定位 </el-button>
					<el-button type="primary" class="ml10" @click="closeClick"> 关闭灯光 </el-button>
				</div>
			</div>
		</el-row>
		<el-table
			:data="state.dataList"
			v-loading="state.loading"
			border
			:cell-style="tableStyle.cellStyle"
			:header-cell-style="tableStyle.headerCellStyle"
			@selection-change="selectionChangHandle"
			@sort-change="sortChangeHandle"
			max-height="620px"
		>
			<el-table-column type="selection" width="40" align="center" />
			<el-table-column type="index" label="序号" width="60" />
			<el-table-column prop="mac" label="标签mac" show-overflow-tooltip />
			<el-table-column prop="manufacture" label="型号" show-overflow-tooltip />
			<el-table-column prop="warehouseName" label="所属仓库" show-overflow-tooltip> </el-table-column>
			<el-table-column prop="routerIp" label="所属基站IP" show-overflow-tooltip />
			<el-table-column prop="routerIdentity" label="所属基站ID" show-overflow-tooltip />
			<el-table-column prop="power" label="电池电量" show-overflow-tooltip />
			<el-table-column prop="rssi" label="信号强度" show-overflow-tooltip />
			<el-table-column label="在线状态" show-overflow-tooltip>
				<template #default="scope">
					{{ scope.row.state ? '是' : '否' }}
				</template>
			</el-table-column>

			<el-table-column prop="showStyle" label="标签显示模板" show-overflow-tooltip />
			<el-table-column prop="tagRegisterEn" label="标签注册开关" show-overflow-tooltip />
			<el-table-column prop="locationCode" label="位置编码" show-overflow-tooltip />
			<el-table-column prop="updateTime" label="更新时间" show-overflow-tooltip />
		</el-table>
		<pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" v-bind="state.pagination" />
	</div>
</template>

<script setup lang="ts">
import { BasicTableProps, useTable } from '/@/hooks/table';
import { pageList, getWarehouse, openObj, closeObj } from '/@/api/basicData/selectionManagement/label';
import { useMessage, useMessageBox } from '/@/hooks/message';

// 搜索变量
const queryRef = ref();
// 多选变量
const selectObjs = ref([]) as any;
const multiple = ref(true);

const state: BasicTableProps = reactive<BasicTableProps>({
	queryForm: {
		warehouseId: void 0,
		mac: '',
		locationCode: '',
		power: '',
	},
	pageList,
});

//  table hook
const { getDataList, currentChangeHandle, sizeChangeHandle, sortChangeHandle, tableStyle } = useTable(state);

// 多选事件
const selectionChangHandle = (objs: { mac: string }[]) => {
	selectObjs.value = objs.map(({ mac }) => mac);
	multiple.value = !objs.length;
};
// 获取仓库数据
let warehouseData = ref<any[]>([]);
const getWarehouseData = () => {
	getWarehouse().then((res: any) => {
		warehouseData.value = res.data;
		if(res.data.length){
			state.queryForm.warehouseId=res.data[0].id;
			getDataList();
		}
	});
};

const openClick = () => {
	if (!selectObjs.value.length) return useMessage().error('请选择标签');
	if (!state.queryForm.warehouseId) return useMessage().error('请选择仓库');
	openObj({ warehouseId: state.queryForm.warehouseId, macList: selectObjs.value })
		.then((res: any) => {
			getDataList(false);
			useMessage().success('操作成功');
		})
		.catch((err: any) => {
			useMessage().error(err.msg);
		});
};
const closeClick = () => {
	if (!state.queryForm.warehouseId) return useMessage().error('请选择仓库');
	closeObj(state.queryForm.warehouseId)
		.then((res: any) => {
			getDataList(false);
			useMessage().success('操作成功');
		})
		.catch((err: any) => {
			useMessage().error(err.msg);
		});
};
onMounted(() => {
	getWarehouseData();
});
</script>
