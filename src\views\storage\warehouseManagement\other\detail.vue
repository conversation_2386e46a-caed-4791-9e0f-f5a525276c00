<template>
	<el-drawer v-model="visible" size="30%" :with-header="false">
		<div class="w-full">
			<el-row>
				<div class="ml-7">
					<span class="mr-3"> 物资名称： </span>
					<span> {{ tableData.materialName }} </span>
				</div>
			</el-row>
			<el-row class="mb-2 mt-2">
				<div>
					<span class="ml-7 mr-3"> 物资编码： </span>
					<span> {{ tableData.materialCode }} </span>
				</div>
			</el-row>
			<el-row>
				<div class="w-full flex justify-between items-center">
					<div class="ml-7">
						<span class="mr-3"> 物资标识： </span>
						<span> {{ tableData.materialIdentify }} </span>
					</div>
				</div>
			</el-row>
			<el-row class="mb8"> </el-row>
			<el-table
				:data="tableData.materialList"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				max-height="85vh"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资条码" prop="materialBar" show-overflow-tooltip></el-table-column>
				<el-table-column label="状态" show-overflow-tooltip width="160">
					<template #default="scope">
						<span v-if="scope.row.onShelfStatus == '0'">待上架</span>
						<span v-if="scope.row.onShelfStatus == '1'">在库</span>
						<span v-if="scope.row.onShelfStatus == '2'">下架</span>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</el-drawer>
</template>

<script setup lang="ts" name="log-detail">
import { getMxObj } from '/@/api/storage/warehouseManagement/purchase';
import { useI18n } from 'vue-i18n';
import { useMessage } from '/@/hooks/message';
import { BasicTableProps, useTable } from '/@/hooks/table';

const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const { t } = useI18n();

// 定义刷新表格emit
const emit = defineEmits(['refresh']);

const visible = ref(false);

// 打开弹窗
const openDialog = async (id: any) => {
	visible.value = true;
	// 加载使用的数据
	getPostData(id);
};
const tableData = ref<any>([]);
const getPostData = async (id: any) => {
	const { data } = await getMxObj(id);
	tableData.value = data;
};
// 暴露变量
defineExpose({
	openDialog,
});
</script>
