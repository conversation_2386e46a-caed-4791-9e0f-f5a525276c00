<template>
	<div class="layout-padding">
		<splitpanes>
			<pane size="15">
				<div class="layout-padding-auto layout-padding-view">
					<el-input v-model="state.queryForm.phone" placeholder="请通过条码扫码录入" clearable />
					<el-row class="mt3 mb5">
						<div class="w-full flex justify-between items-center">
							<div>
								<span>读取标签数：</span><span class="text-blue-500">{{ barList.length }}</span>
							</div>
							<div>
								<el-button
									text
									type="primary"
									icon="delete"
									@click="
										() => {
											handleStopRFID();
											barList = [];
										}
									"
								>
								</el-button>
							</div>
						</div>
					</el-row>
					<ul class="overflow-auto h-[80vh]">
						<li v-for="i in barList" :key="i" class="infinite-list-item">{{ i }}</li>
					</ul>
					<el-row class="mt12">
						<div class="w-full flex justify-evenly items-center">
							<el-button type="primary" @click="handleRead()">读标签 </el-button>
							<el-button color="#FF7D00" type="primary" @click="matchClick" style="color: white !important">匹配 </el-button>
						</div>
					</el-row>
				</div>
			</pane>
			<!-- 右侧 -->
			<pane class="ml8">
				<div class="layout-padding-auto layout-padding-view">
					<el-row class="mt7">
						<div class="text-xl font-bold">出库</div>
					</el-row>

					<el-row class="mt20">
						<el-col :span="24">
							<Descriptions :column="4" :label-style="{ fontSize: '14px' }">
								<template #extra> </template>

								<DescriptionsItem label="出库单编号">{{ detailsData?.billCode }}</DescriptionsItem>
								<DescriptionsItem label="出库仓库">{{ detailsData?.warehouseName }}</DescriptionsItem>
								<DescriptionsItem label="出库单状态">{{
									detailsData?.billStatus == 0 ? '待确认' : detailsData?.billStatus == 1 ? '待出库' : detailsData?.billStatus == 2 ? '已出库' : ''
								}}</DescriptionsItem>

								<DescriptionsItem label="出库用途">{{ detailsData?.outPurpose }}</DescriptionsItem>
								<DescriptionsItem label="是否归还">{{
									detailsData?.needReturn == 1 ? '是' : detailsData?.needReturn == 0 ? '否' : ''
								}}</DescriptionsItem>
								<DescriptionsItem label="预计归还时间" v-if="detailsData?.needReturn == 1">{{ detailsData?.planReturnTime }}</DescriptionsItem>
								<DescriptionsItem label="申请部门">{{ detailsData?.applyDept }}</DescriptionsItem>
								<DescriptionsItem label="申请人员">{{ detailsData?.applyUser }}</DescriptionsItem>
								<DescriptionsItem label="创建人">{{ detailsData?.createBy }}</DescriptionsItem>
								<DescriptionsItem label="创建时间">{{ detailsData?.createTime }}</DescriptionsItem>
								<DescriptionsItem label="出库人">{{ detailsData?.outUser }}</DescriptionsItem>
								<DescriptionsItem label="出库时间">{{ detailsData?.outTime }}</DescriptionsItem>
							</Descriptions>
						</el-col>
					</el-row>
					<el-table
						height="calc(100vh - 335px)"
						:data="tableData"
						border
						empty-text="暂无数据，请从左侧添加入库信息"
						:cell-style="tableStyle.cellStyle"
						:header-cell-style="tableStyle.headerCellStyle"
						ref="tableRefs"
						class="w-full"
					>
						<el-table-column label="序号" type="index" width="60" fixed="left" />

						<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
						<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="200"></el-table-column>
						<el-table-column label="数量" prop="num" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip width="60"></el-table-column>
						<el-table-column label="条码数量" show-overflow-tooltip width="260">
							<template #default="scope">
								<span :class="scope.row.d > scope.row.num ? 'text-red-500' : scope.row.d == scope.row.num ? 'text-green-500' : 'text-black'">{{
									scope.row.d
								}}</span>
							</template>
						</el-table-column>
						<el-table-column label="条码明细" show-overflow-tooltip width="280" class-name="custom-class" fixed="right">
							<template #header>
								<div>
									<span>条码明细</span>
									<el-tooltip class="box-item" effect="light" content="全部收起" placement="top-start">
										<el-button
											v-if="tableData.some((row:any) => needsExpansion(row.barCode))"
											link
											type="primary"
											size="small"
											@click="toggleAllExpand"
										>
											<img :src="zdImg" v-show="isAllExpanded" />
										</el-button>
									</el-tooltip>
								</div>
							</template>
							<template #default="scope">
								<div v-if="scope.row.barCode">
									<div class="flex justify-items-start">
										<div>
											<div v-for="(code, index) in getDisplayCodes(scope.row)" :key="index">
												<span>{{ code }}</span>
												<span>&nbsp;&nbsp;&nbsp;</span>
												<el-button text type="primary" icon="delete" @click="delCode(scope.row, code)"> </el-button>
												<span>&nbsp;&nbsp;&nbsp;</span>
											</div>
										</div>

										<el-button
											v-if="needsExpansion(scope.row.barCode)"
											link
											type="primary"
											size="small"
											@click="toggleExpand(scope.row, scope.$index)"
										>
											<img :src="scope.row.isExpanded ? zdImg : zkImg" :alt="scope.row.isExpanded ? '收起' : '展开'" />
										</el-button>
									</div>
								</div>
							</template>
						</el-table-column>
					</el-table>
					<el-row>
						<div class="w-full mt15">
							<div class="float-left">物资清单（当前总数量 {{ tableData.reduce((sum: any, item: any) => sum + item.d, 0) }} ）</div>
							<div class="float-right">
								<el-button @click="returnClick">{{ $t('common.cancelButtonText') }}</el-button>
								<el-button @click="confirmClick" type="primary" :disabled="loading">确认出库</el-button>
							</div>
						</div>
					</el-row>
				</div>
			</pane>
		</splitpanes>
		<not-matched ref="notMatchedRef" />
	</div>
</template>

<script lang="ts" name="systemUser" setup>
import { getOutboundObj, filterTags, submitObj } from '/@/api/storage/outbound';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage } from '/@/hooks/message';
import notMatched from './notMatched.vue';
import { useI18n } from 'vue-i18n';
import { getConfigurationObj } from '/@/api/storage/rfidSettings';
import axios from 'axios';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const notMatchedRef = ref();
// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});

const { tableStyle } = useTable(state);

//确认btn
const loading = ref(false);

//删除条码明细
const delCode = (row: any, code: string) => {
	tableData.value = tableData.value.map((item: any) => {
		if (item.barCode !== row.barCode) return item;
		const updatedCodes = item.barCode.split(',').filter((barCode: any) => barCode !== code);
		return {
			...item,
			barCode: updatedCodes.join(','),
			d: updatedCodes.length,
		};
	});
};

//右侧提交
const confirmClick = async () => {
	//判断tableData 中的d 是否全部等于num
	if (tableData.value.some((item: any) => item.d !== item.num)) {
		useMessage().error('请先完成物资条码匹配');
		return;
	}

	try {
		loading.value = true;
		const submitBillDetailList = tableData.value.map((item: any) => {
			return {
				outWareBillDetaId: item.id,
				materialCatalogId: item.materialCatalogId,
				materialCode: item.materialCode,
				materialBarList: item.barCode ? item.barCode.split(',') : [],
			};
		});
		await submitObj({
			id: route.query?.id,
			submitBillDetailList,
		});
		useMessage().success(t('common.addSuccessText'));
		router.replace({
			path: '/storage/allocate/outbound/index',
		});
	} catch (err: any) {
		useMessage().error(err.msg);
	} finally {
		loading.value = false;
	}
};

const tableData = ref<any>([]);
const detailsData = ref<any>();

//匹配事件
const matchClick = async () => {
	handleStopRFID();
	clearInterval(readRFIDtimer.value);
	if (!barList.value.length) return useMessage().error('请先读取标签');

	await goFilterTags();
	const obj = correctBarList.value.reduce(
		(acc: any, item: any) => ((acc[item.slice(12, 18)] = acc[item.slice(12, 18)] ? [...acc[item.slice(12, 18)], item] : [item]), acc),
		{}
	);

	tableData.value.forEach((item: any) => {
		const newBarCode = obj[item.materialCode];
		if (newBarCode) {
			const currentBarCodes = item.barCode ? item.barCode.split(',') : [];
			item.barCode = [...new Set([...currentBarCodes, ...newBarCode])].join(',');
			item.d = item.barCode.split(',').length;
		}
	});
	//TODO 匹配成功后，开启读取标签功能
};
const goFilterTags = async () => {
	correctBarList.value = [];
	try {
		const { data } = await filterTags({
			outwareId: route.query?.id,
			materialBarList: barList.value,
		});

		if (data && data.length) {
			notMatchedRef.value.openDialog(data);
			correctBarList.value = barList.value.filter((item: any) => !data.includes(item));
		} else {
			correctBarList.value = [...barList.value];
		}
	} catch (error) {}
};

const correctBarList = ref<any>([]);
const getDataObj = async () => {
	let { data } = await getOutboundObj(route.query?.id);
	detailsData.value = data;
	tableData.value = data.billDetailVOList.map((item: any) => ({ ...item, d: 0, barCode: '', isExpanded: false }));
};

onMounted(async () => {
	await getDataObj();
});

const barList = ref<any>([]);
//返回上级菜单
const returnClick = () => {
	router.replace({
		path: '/storage/allocate/outbound/index',
	});
};
// 表格内展开折叠
import zkImg from '/@/assets/flod.png';
import zdImg from '/@/assets/open.png';
const MAX_DISPLAY_LINES = 2;
//table ref
const tableRefs = ref();
//表格数据
//全部数据，截取最大行数   截取字段为row.barCode
const getDisplayCodes = (row: any) => {
	const barCode = row.barCode;
	if (!barCode) return [];
	const codes = barCode.split(',');
	if (codes.length > MAX_DISPLAY_LINES && !row.isExpanded) {
		return [...codes.slice(0, MAX_DISPLAY_LINES - 1)];
	}
	return codes;
};
//table滚动到指定行
const scrollToRow = (tableRef: any, rowIndex: number) => {
	nextTick(() => {
		if (tableRef.value) {
			// @ts-ignore
			const tableBody = tableRef.value.$el.querySelector('.el-table__body-wrapper');
			if (tableBody) {
				const row = tableBody.querySelectorAll('.el-table__row')[rowIndex - 1];
				if (row) {
					row.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				}
			}
		}
	});
};
//将所有行 收起
const toggleAllExpand = () => {
	tableData.value.forEach((row: any) => {
		row.isExpanded = false;
	});
};
//有展开的行  则展示表头收起图标
const isAllExpanded = computed(() => tableData.value.some((row: any) => row.isExpanded));
//判断长度需要展开字段的数据长度
const needsExpansion = (barCode?: string) => {
	return barCode ? barCode.split(',').length > MAX_DISPLAY_LINES : false;
};
//行内展开收起事件
const toggleExpand = (row: any, rowIndex: any) => {
	row.isExpanded = !row.isExpanded;
	if (!row.isExpanded) scrollToRow(tableRefs, rowIndex + 1);
};

//获取详情
let Ip = ref('');
let Port = ref('');
let readRFIDtimer = ref<any>(null);
const getConfiguration = () => {
	getConfigurationObj().then((res: any) => {
		Ip.value = res.data.rfidDeviceWorkbenchIp;
		Port.value = res.data.port;
	});
};
//读标签
const handleRead = () => {
	if (readRFIDtimer.value) {
		return;
	}
	const RFIDurl = 'http://' + Ip.value + ':' + Port.value;
	let formData = { cmd: '10004', data: { time: 0 } };
	axios
		.post(RFIDurl, formData, { headers: { 'Content-Type': 'application/json' } })
		.then((res: any) => {
			let status = res.data.data.status;
			if (status == 0) {
				useMessage().success('启动读卡成功');
				readRFIDtimer.value = setInterval(() => {
					handleReadRFID();
				}, 1000);
			} else {
				useMessage().error('启动读卡失败，请重新启动！');
			}
		})
		.catch((error: any) => {
			useMessage().error('请确保RFID工作台通信正常，参数配置正确！');
		});
};

// 读标签
const handleReadRFID = () => {
	const RFIDurl = 'http://' + Ip.value + ':' + Port.value;
	let formData = { cmd: '10006' };
	axios.post(RFIDurl, formData, { headers: { 'Content-Type': 'application/json' } }).then((res) => {
		let status = res.data.data.status;
		if (status == 0) {
			let data = res.data.data.epcs;
			data.forEach((item: any) => {
				if (!barList.value.includes(item)) {
					barList.value.push(item);
				}
			});
		} else {
			useMessage().error('标签数据获取失败，错误编码：' + status);
		}
	});
};
// 停止读写器
const handleStopRFID = () => {
	let formData = { cmd: '10005' };
	const RFIDurl = 'http://' + Ip.value + ':' + Port.value;
	axios.post(RFIDurl, formData, { headers: { 'Content-Type': 'application/json' } }).then((res) => {
		let status = res.data.data.status;
		if (status == 0) {
			useMessage().success('停止读卡成功');
			clearInterval(readRFIDtimer.value);
			readRFIDtimer.value = null;
		} else {
			useMessage().error('停止读卡失败，错误编码：' + status);
			clearInterval(readRFIDtimer.value);
			readRFIDtimer.value = null;
		}
	});
};

onMounted(() => {
	getConfiguration();
});
// 在组件卸载时清除定时器
onBeforeUnmount(() => {
	if (readRFIDtimer.value) {
		let formData = { cmd: '10005' };
		const RFIDurl = 'http://' + Ip.value + ':' + Port.value;
		axios.post(RFIDurl, formData, { headers: { 'Content-Type': 'application/json' } }).then(() => {});
		clearInterval(readRFIDtimer.value);
	}
});
</script>
<style lang="scss"></style>
