<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<el-row class="mt20 mb-2">
				<el-col :span="24">
					<Descriptions title="" :column="4" :label-style="{ fontSize: '14px' }">
						<template #extra> </template>

						<DescriptionsItem label="出库单编号">{{ detailsData?.billCode }}</DescriptionsItem>
						<DescriptionsItem label="出库仓库">{{ detailsData?.warehouseName }}</DescriptionsItem>
						<DescriptionsItem label="出库单状态">{{
							detailsData?.billStatus == 0 ? '待确认' : detailsData?.billStatus == 1 ? '待出库' : detailsData?.billStatus == 2 ? '已出库' : ''
						}}</DescriptionsItem>

						<DescriptionsItem label="出库用途">{{ detailsData?.outPurpose }}</DescriptionsItem>
						<DescriptionsItem label="是否归还">{{ detailsData?.needReturn == 1 ? '是' : detailsData?.needReturn == 0 ? '否' : '' }}</DescriptionsItem>
						<DescriptionsItem label="预计归还时间" v-if="detailsData?.needReturn == 1">{{ detailsData?.planReturnTime }}</DescriptionsItem>
						<DescriptionsItem label="申请部门">{{ detailsData?.applyDept }}</DescriptionsItem>
						<DescriptionsItem label="申请人员">{{ detailsData?.applyUser }}</DescriptionsItem>
						<DescriptionsItem label="创建人">{{ detailsData?.createBy }}</DescriptionsItem>
						<DescriptionsItem label="创建时间">{{ detailsData?.createTime }}</DescriptionsItem>
						<DescriptionsItem label="出库人">{{ detailsData?.outUser }}</DescriptionsItem>
						<DescriptionsItem label="出库时间">{{ detailsData?.outTime }}</DescriptionsItem>
					</Descriptions>
				</el-col>
			</el-row>

			<el-row>
				<div class="mb-2 font-bold">出库物资清单</div>
			</el-row>
			<el-table
				v-loading="state.loading"
				:data="detailsData?.billDetailVOList"
				max-height="calc(100vh - 355px)"
				border
				:cell-style="tableStyle.cellStyle"
				:header-cell-style="tableStyle.headerCellStyle"
				ref="tableRefs"
			>
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" fixed="left" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip width="600"></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="单位" prop="basicUnit" show-overflow-tooltip></el-table-column>
			</el-table>
			<el-row class="fixed bottom-[15px] right-[20px]">
				<el-button @click="returnClick">取消</el-button>
				<el-button type="primary" @click="confirmClick">确认</el-button>
			</el-row>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { getObj, confirmObj } from '/@/api/storage/outbound';

import { BasicTableProps, useTable } from '/@/hooks/table';
import { useMessage} from '/@/hooks/message';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const route = useRoute();

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const router = useRouter();
const returnClick = () => {
	router.replace({ path: '/storage/Outbound/index' });
};
const confirmClick = async () => {
	try {
		await confirmObj(route.query?.id).then(() => {
			useMessage().success('确认成功');
			router.replace({ path: '/storage/Outbound/index' });
		});
	} catch (err: any) {
		useMessage().error(err.msg);
	}
};
const detailsData = ref<any>();
onMounted(async () => {
	await getObj(route.query?.id).then((res) => {
		detailsData.value = res.data;
	});
});
</script>

<style lang="scss" scoped>
pre code.hljs {
	width: 65%;
}
</style>
