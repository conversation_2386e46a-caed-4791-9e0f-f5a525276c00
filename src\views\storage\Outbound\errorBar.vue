<template>
	<div class="system-user-dialog-container">
		<el-dialog :close-on-click-modal="false" draggable v-model="visible" height="200" width="800">

            <el-row class="mb4">
                <span> 以下物资库存不足： </span>
			</el-row>
			<el-table :data="tableData" border :cell-style="tableStyle.cellStyle" :header-cell-style="tableStyle.headerCellStyle" max-height="400">
				<el-table-column label="序号" type="index" width="60" fixed="left" />
				<el-table-column label="物资编码" prop="materialCode" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资标识" prop="materialIdentify" show-overflow-tooltip></el-table-column>
				<el-table-column label="物资名称" prop="materialName" show-overflow-tooltip></el-table-column>
				<el-table-column label="数量" prop="num" show-overflow-tooltip></el-table-column>
				<el-table-column label="库存"  show-overflow-tooltip>
                    <template #default="scope">
                        <span class="text-red-600"> {{ scope.row.inventoryNum }}</span>
                    </template>
                </el-table-column>
			</el-table>


		</el-dialog>
	</div>
</template>

<script lang="ts" name="systemUserDialog" setup>
import { useI18n } from 'vue-i18n';
import { BasicTableProps, useTable } from '/@/hooks/table';

const state: BasicTableProps = reactive<BasicTableProps>({});
const { tableStyle } = useTable(state);

const { t } = useI18n();

// 定义变量内容
const visible = ref(false);
let tableData = ref<any[]>([]);

// 打开弹窗
const openDialog = async ( barList: any) => {
	tableData.value = barList;
	visible.value = true;
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
